import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { mutation, query } from './_generated/server';
import { getUser } from './auth';

export const uploadAvatar = mutation({
    args: {
        file: v.id("_storage"),
    },
    handler: async (ctx, args) => {
        const user = await getUser(ctx);
        return await ctx.db.patch(  (user as any)._id, {
            avatar: args.file,
        });
    },
});

export const avatarUrlFromId = mutation({
    args: {
        id: v.id("_storage"),
    },
    handler: async (ctx, args) => {
        return await ctx.storage.getUrl(args.id);
    }
})


export const generatedUploadUrl = mutation({
    args: {},
    handler: async (ctx) => {
        return await ctx.storage.generateUploadUrl();
    }
})

export const createContact = mutation({
    args: {
        email: v.optional(v.string()),
        name: v.string(),
        phone: v.optional(v.string()),
        avatar: v.optional(v.id("_storage")),
        avatarUrl: v.optional(v.string()),
        jobTitle: v.optional(v.string()),
        location: v.optional(v.string()),
        linkedin: v.optional(v.string()),
        notes: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const user = await getUser(ctx);
        return await ctx.db.insert('contacts', {
            userId: (user as any)._id,
            avatar: args.avatar,
            avatarUrl: args.avatarUrl,
            email: args.email,
            name: args.name,
            phone: args.phone,
            jobTitle: args.jobTitle,
            location: args.location,
            linkedin: args.linkedin,
            notes: args.notes,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            searchSummary: args.name + " " + args.email + " " + args.phone + " " + args.jobTitle + " " + args.location + " " + args.linkedin + " " + args.notes,
        });
    },
});

export const getContacts = query({
    handler: async (ctx) => {
        const user = await getUser(ctx);
        return await ctx.db.query('contacts')
            .withIndex('userId', (q) => q.eq('userId', (user as any)._id))
            .collect();
    },
});

export const getContactById = query({
    args: { id: v.string() },
    handler: async (ctx, args) => {
        const user = await getUser(ctx);
        const contact = await ctx.db.get(args.id as Id<"contacts">);
        
        // Ensure the contact belongs to the current user
        if (contact && contact.userId === (user as any)._id) {
            return contact;
        }
        
        return null;
    },
});

export const updateContact = mutation({
    args: {
        id: v.string(),
        name: v.string(),
        email: v.optional(v.string()),
        phone: v.optional(v.string()),
        jobTitle: v.optional(v.string()),
        location: v.optional(v.string()),
        linkedin: v.optional(v.string()),
        notes: v.optional(v.string()),
        avatar: v.optional(v.id("_storage")),
        avatarUrl: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const user = await getUser(ctx);
        const contact = await ctx.db.get(args.id as Id<"contacts">);
        
        // Ensure the contact exists and belongs to the current user
        if (!contact || contact.userId !== (user as any)._id) {
            throw new Error("Contact not found or access denied");
        }
        
        return await ctx.db.patch(args.id as Id<"contacts">, {
            name: args.name,
            email: args.email,
            phone: args.phone,
            jobTitle: args.jobTitle,
            location: args.location,
            linkedin: args.linkedin,
            notes: args.notes,
            ...(args.avatar && { avatar: args.avatar }),
            ...(args.avatarUrl && { avatarUrl: args.avatarUrl }),
            updatedAt: Date.now(),
            searchSummary: `${args.name} ${args.email || ''} ${args.phone || ''} ${args.jobTitle || ''} ${args.location || ''} ${args.linkedin || ''} ${args.notes || ''}`
        });
    },
});

export const deleteContact = mutation({
    args: {
        contactIds: v.array(v.string()),
    },
    handler: async (ctx, args) => {
        const user = await getUser(ctx);
        return await args.contactIds.forEach((contactId) => {
            ctx.db.delete(contactId as Id<"contacts">);
        });
    },
})