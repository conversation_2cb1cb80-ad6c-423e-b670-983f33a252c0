{"expo": {"name": "ai-caption-editor", "slug": "ai-caption-editor", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "scheme": "aicaptioneditor", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.anonymous.aicaptioneditor"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"], "package": "com.anonymous.aicaptioneditor"}, "web": {"bundler": "metro", "output": "static", "favicon": "./src/assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./src/assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", ["expo-build-properties", {"ios": {"deploymentTarget": "16.0"}}], "expo-video", "expo-audio", ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "ai-caption-editor", "organization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]], "experiments": {"typedRoutes": true}}}