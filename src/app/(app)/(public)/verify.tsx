import { emailAtom } from '@/store/login';
import { useTheme } from '@/hooks/useTheme';
import { isClerkAPIResponseError, useSignIn, useSignUp } from '@clerk/clerk-expo';
import MaterialCommunityIcon from '@expo/vector-icons/MaterialCommunityIcons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useAtomValue } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import { Alert, Keyboard, KeyboardAvoidingView, Platform, Text, TextInput, TouchableOpacity, View } from 'react-native';

const Page = () => {
    const { isLogin } = useLocalSearchParams<{ isLogin?: string }>();
    const router = useRouter();
    const { theme } = useTheme();
    const isDarkMode = theme === 'dark';

    const [code, setCode] = useState(['', '', '', '', '', '']);
    const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null, null, null]);
    const [countdown, setCountdown] = useState(60);
    const email = useAtomValue(emailAtom);

    const { signUp, setActive } = useSignUp();
    const { signIn, setActive: setActiveSignIn } = useSignIn();

    const isCodeComplete = code.every(c => c.length === 1);
    const [isTimerRunning, setIsTimerRunning] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        inputRefs.current[0]?.focus();
    }, []);

    useEffect(() => {
        if (isCodeComplete) {
            Keyboard.dismiss();
        }
    }, [isCodeComplete]);

    useEffect(() => {
        let timer: ReturnType<typeof setInterval>;
        if (isTimerRunning && countdown > 0) {
            timer = setInterval(() => {
                setCountdown(prev => prev - 1);
            }, 1000);
        } else if (countdown === 0) {
            setIsTimerRunning(false);
        }
        return () => clearInterval(timer);
    }, [countdown, isTimerRunning]);

    const handleCodeChange = (text: string, index: number) => {
        const newCode = [...code];
        newCode[index] = text;
        setCode(newCode);

        if (text && index < code.length - 1) {
            inputRefs.current[index + 1]?.focus();
        }
    };

    const handleResendCode = async () => {
        if (countdown > 0) return;
        setCountdown(60);
        setIsTimerRunning(true);
        try {
            if (isLogin === 'true') {
                // For sign-in flow, we need to create a new sign-in attempt
                await signIn!.create({
                    strategy: 'email_code',
                    identifier: email,
                });
            } else {
                // For sign-up flow
                await signUp!.prepareEmailAddressVerification({ strategy: 'email_code' });
            }
        } catch (error) {
            console.error('Resend code error:', error);
            Alert.alert('Error', 'Failed to resend code. Please try again.');
        }
    };

    const handleSignIn = async () => {
        if (isSubmitting) return;
        
        const verificationCode = code.join('').trim();
        
        if (verificationCode.length !== 6) {
            Alert.alert('Error', 'Please enter a complete 6-digit code.');
            return;
        }

        setIsSubmitting(true);
        try {
            const result = await signIn!.attemptFirstFactor({
                strategy: 'email_code',
                code: verificationCode
            });
            
            if (result.createdSessionId) {
                await setActiveSignIn!({ session: result.createdSessionId });
                router.replace('/');
            }

        } catch (error) {
            console.error('Sign in error:', error);
            if (isClerkAPIResponseError(error)) {
                const errorMessage = error.errors[0].message;
                
                // Handle specific error cases
                if (errorMessage.includes('expired')) {
                    Alert.alert(
                        'Code Expired',
                        'Your verification code has expired. Please request a new one.',
                        [
                            {
                                text: 'Resend Code',
                                onPress: handleResendCode
                            },
                            {
                                text: 'OK',
                                style: 'cancel'
                            }
                        ]
                    );
                } else if (errorMessage.includes('invalid') || errorMessage.includes('incorrect')) {
                    Alert.alert('Invalid Code', 'The code you entered is invalid. Please check and try again.');
                    // Clear the code inputs for retry
                    setCode(['', '', '', '', '', '']);
                    inputRefs.current[0]?.focus();
                } else {
                    Alert.alert('Error', errorMessage);
                }
            } else {
                Alert.alert('Error', 'Failed to verify code. Please try again.');
            }
        } finally {
            setIsSubmitting(false);
            router.replace('/contacts');
        }
    };

    const handleCreateAccount = async () => {
        if (isSubmitting) return;
        
        const verificationCode = code.join('').trim();
        
        if (verificationCode.length !== 6) {
            Alert.alert('Error', 'Please enter a complete 6-digit code.');
            return;
        }

        setIsSubmitting(true);
        try {
            const result = await signUp!.attemptEmailAddressVerification({
                code: verificationCode
            });
            
            if (result.createdSessionId) {
                await setActive!({ session: result.createdSessionId });
                router.replace('/');
            }

        } catch (error) {
            console.error('Sign up error:', error);
            if (isClerkAPIResponseError(error)) {
                const errorMessage = error.errors[0].message;
                
                // Handle specific error cases
                if (errorMessage.includes('already been verified')) {
                    Alert.alert(
                        'Already Verified',
                        'This email has already been verified. Please try signing in instead.',
                        [
                            {
                                text: 'Sign In',
                                onPress: () => router.replace('/login')
                            },
                            {
                                text: 'OK',
                                style: 'cancel'
                            }
                        ]
                    );
                } else if (errorMessage.includes('expired')) {
                    Alert.alert(
                        'Code Expired',
                        'Your verification code has expired. Please request a new one.',
                        [
                            {
                                text: 'Resend Code',
                                onPress: handleResendCode
                            },
                            {
                                text: 'OK',
                                style: 'cancel'
                            }
                        ]
                    );
                } else {
                    Alert.alert('Error', errorMessage);
                }
            } else {
                Alert.alert('Error', 'Failed to verify code. Please try again.');
            }
        } finally {
            setIsSubmitting(false);
            router.replace('/contacts');
        }
    };

    return (
        <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className={`flex-1 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
            <View className={`flex-1 px-6 pt-safe ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
                <TouchableOpacity className={`w-12 h-12 justify-center items-center rounded-xl mb-4 ${isDarkMode ? 'bg-gray-800 active:bg-gray-700' : 'bg-gray-200 active:bg-gray-300'}`} onPress={() => router.back()}>
                    <MaterialCommunityIcon name='chevron-left' size={28} color={isDarkMode ? 'white' : 'black'} />
                </TouchableOpacity>
                <Text className={`text-3xl font-Poppins_600SemiBold mt-20 mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Enter code</Text>
                <Text className={`text-base font-Poppins_400Regular leading-6 mb-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Check your email and enter the code sent to
                </Text>
                <Text className={`font-Poppins_500Medium text-base ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{email}</Text>

                {/* Code inputs */}
                <View className="flex-row justify-center mt-10 mb-8 gap-3">
                     {['0', '1', '2', '3', '4', '5'].map((index) => (
                       <TextInput
                           key={index}
                           ref={(ref) => { inputRefs.current[parseInt(index)] = ref; }}
                           className={`w-[56px] h-[56px] rounded-xl text-center text-2xl font-Poppins_600SemiBold border-2 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900'} ${!code[parseInt(index)] && parseInt(index) === code.findIndex(c => !c) ? 'border-primary' : (isDarkMode ? 'border-gray-700' : 'border-gray-300')} ${code[parseInt(index)] ? (isDarkMode ? 'border-gray-600' : 'border-gray-400') : ''}`}
                           maxLength={1}
                           keyboardType="number-pad"
                           value={code[parseInt(index)]}
                           caretHidden={true}
                           selectionColor="transparent"
                           onChangeText={(text) => handleCodeChange(text, parseInt(index))}
                           onKeyPress={({nativeEvent}) => {
                            if (nativeEvent.key === 'Backspace') {
                                const newCode = [...code];
                                newCode[parseInt(index)] = '';
                                setCode(newCode);
                                if (parseInt(index) > 0) {
                                    inputRefs.current[parseInt(index) - 1]?.focus();
                                }
                            }
                           }}
                       />
                    ))}
                </View>
                

                {/* Resend Code */}
                <View className="items-center mb-6">
                    <TouchableOpacity
                        onPress={handleResendCode}
                        disabled={countdown > 0}
                        className="py-2 px-4"
                    >
                        <Text className={`font-Poppins_500Medium text-base ${countdown > 0 ? (isDarkMode ? 'text-gray-400' : 'text-gray-500') : 'text-primary'}`}>
                            Resend code {countdown > 0 && `(${countdown}s)`}
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Create account button */}
                <TouchableOpacity
                    disabled={!isCodeComplete || isSubmitting}
                    className={`rounded-xl py-4 mt-auto mb-8 ${isCodeComplete && !isSubmitting ? 'bg-primary' : (isDarkMode ? 'bg-gray-800' : 'bg-gray-200')} ${!isCodeComplete || isSubmitting ? 'opacity-60' : ''}`}
                    onPress={isLogin ? handleSignIn : handleCreateAccount}
                >
                    <Text className={`text-center text-lg font-Poppins_600SemiBold ${isCodeComplete && !isSubmitting ? 'text-white' : (isDarkMode ? 'text-gray-400' : 'text-gray-600')}`}>
                        {isSubmitting ? 'Verifying...' : (isLogin ? 'Sign In' : 'Create Account')}
                    </Text>
                </TouchableOpacity>

            </View>
        </KeyboardAvoidingView>
    )
}

export default Page;