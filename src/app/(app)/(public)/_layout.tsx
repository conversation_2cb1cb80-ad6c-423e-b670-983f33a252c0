import { ThemeProvider } from '@/hooks/useTheme';
import { Stack } from "expo-router";

const Page = () => {
  return (
    <ThemeProvider>
      <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="login" />
          <Stack.Screen name="verify" />
          <Stack.Screen name="faq" options={({ presentation: 'modal'})}/>
      </Stack>
    </ThemeProvider>
  );
}

export default Page;