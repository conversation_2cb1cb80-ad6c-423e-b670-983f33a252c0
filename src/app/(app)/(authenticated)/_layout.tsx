import { useAuth } from "@clerk/clerk-expo";
import { Stack, useRouter } from "expo-router";

const Layout = () => {
    const { isSignedIn } = useAuth();
    const guard = isSignedIn ?? false;
    const router = useRouter();
    return (
        <Stack screenOptions={{ headerShown: false }}>
            <Stack.Protected guard={guard}>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="contacts/new" options={{ headerShown: false }} />
                <Stack.Screen name="contacts/[id]" options={{ headerShown: false }} />
                <Stack.Screen name="contacts/[id]/edit" options={{ headerShown: false }} />
            </Stack.Protected>
        </Stack>
    )
}

export default Layout;