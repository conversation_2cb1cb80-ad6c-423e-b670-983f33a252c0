import { Text, View } from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';

export default function AiAssistant() {
    const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#FCFCFC',
    text: isDarkMode ? '#fff' : '#000',
  };

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
      <Text style={{ color: colors.text }}>AI Assistant</Text>
    </View>
  );
}
