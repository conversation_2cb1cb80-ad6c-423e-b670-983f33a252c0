import { HapticTab } from '@/app/components/HapticTab';
import { ContactsEditProvider, useContactsEdit } from '@/contexts/ContactsEditContext';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';
import { Feather, Ionicons } from '@expo/vector-icons';
import { PlatformPressable } from '@react-navigation/elements';
import { Tabs, useRouter } from 'expo-router';
import React from 'react';
import { Text } from 'react-native';

const ContactsHeaderLeft = () => {
  const { theme } = useTheme();
  const { isEditMode, toggleEditMode } = useContactsEdit();
  const isDarkMode = theme === 'dark';
  
  const colors = {
    activeTab: isDarkMode ? '#fff' : '#000',
  };



  return (
    <PlatformPressable style={{ marginLeft: 20 }} onPress={toggleEditMode}>
      <Text style={{ 
        fontSize: 17, 
        fontFamily: 'Poppins_400Regular', 
        color: colors.activeTab
      }}>
        {isEditMode ? 'Done' : 'Remove'}
      </Text>
    </PlatformPressable>
  );
};

const ContactsHeaderRight = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const { selectedContacts, handleDeleteContacts } = useContactsEdit();
  const isDarkMode = theme === 'dark';
  
  const colors = {
    text: isDarkMode ? '#fff' : '#000',
    activeTab: isDarkMode ? '#4A90E2' : '#2563EB',
    destructive: '#FF3B30',
  };

  const hasSelectedContacts = selectedContacts.size > 0;

  return (
    <PlatformPressable 
      style={{ marginRight: 20 }} 
      onPress={hasSelectedContacts ? handleDeleteContacts : () => router.push('/(app)/(authenticated)/contacts/new')}
    >
      {hasSelectedContacts ? (
        <Feather name="trash-2" size={24} color={colors.destructive} />
      ) : (
        <Feather name="plus" size={24} color={colors.text} />
      )}
    </PlatformPressable>
  );
};

const Layout = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const isDarkMode = theme === 'dark';

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#FCFCFC',
    text: isDarkMode ? '#fff' : '#000',
    activeTint: isDarkMode ? '#fff' : (twFullConfig.theme.colors as any).primary,
    inactiveTint: isDarkMode ? '#6c6c6c' : '#999',
    borderColor: isDarkMode ? '#494949' : '#E0E0E0',
  };

  return (
    <ContactsEditProvider>
      <Tabs
        screenOptions={{
          tabBarStyle: {
            backgroundColor: colors.background,
            height: 100,
            elevation: 0,
            borderTopColor: colors.borderColor,
          },
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerTitleStyle: {
            fontFamily: 'Poppins_700Bold',
            fontSize: 28,
          },
          headerTintColor: colors.text,
          tabBarLabelStyle: {
            fontSize: 12,
            fontFamily: 'Poppins_500Medium',
          },
          tabBarActiveTintColor: colors.activeTint,
          tabBarInactiveTintColor: colors.inactiveTint,
          tabBarButton: HapticTab,
        }}>
      <Tabs.Screen
        name="contacts"
        options={{
          title: 'Contacts',
          headerLeft: () => <ContactsHeaderLeft />,
          headerRight: () => <ContactsHeaderRight />,
          tabBarIcon: ({ color, size }) => <Ionicons name="people-outline" color={color} size={size} />,
          tabBarButton: (props) => (
            <PlatformPressable {...props} style={{ gap: 4, flex: 1, alignItems: 'center', marginTop: 10 }} />
          ),
        }}
      />
      <Tabs.Screen
        name="search"
        options={{
          title: 'Search',
          tabBarIcon: ({ color, size }) => <Ionicons name="search-outline" color={color} size={size} />,
          tabBarButton: (props) => (
            <PlatformPressable {...props} style={{ gap: 4, flex: 1, alignItems: 'center', marginTop: 10 }} />
          ),
        }}
      />
      <Tabs.Screen
        name="ai-assistant"
        options={{
          title: 'AI Assistant',
          tabBarIcon: ({ color, size }) => <Ionicons name="sparkles-outline" color={color} size={size} />,
          tabBarButton: (props) => (
            <PlatformPressable {...props} style={{ gap: 4, flex: 1, alignItems: 'center', marginTop: 10 }} />
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color, size }) => <Ionicons name="settings-outline" color={color} size={size} />,
          tabBarButton: (props) => (
            <PlatformPressable {...props} style={{ gap: 4, flex: 1, alignItems: 'center', marginTop: 10 }} />
          ),
        }}
      />
          </Tabs>
    </ContactsEditProvider>
  );
};

export default Layout;
