import { Text, View, TouchableOpacity, StyleSheet } from 'react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';

export default function Settings() {
  const { theme } = useTheme();
  const { signOut } = useAuth();
  const isDarkMode = theme === 'dark';

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#FCFCFC',
    text: isDarkMode ? '#fff' : '#000',
    buttonBackground: isDarkMode ? '#374151' : '#e5e7eb',
    buttonText: isDarkMode ? '#fff' : '#000',
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Text style={[styles.title, { color: colors.text }]}>Settings</Text>
      
      <TouchableOpacity 
        style={[styles.signOutButton, { backgroundColor: colors.buttonBackground }]} 
        onPress={handleSignOut}
      >
        <Text style={[styles.signOutButtonText, { color: colors.buttonText }]}>
          Sign Out
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 40,
  },
  signOutButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  signOutButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
