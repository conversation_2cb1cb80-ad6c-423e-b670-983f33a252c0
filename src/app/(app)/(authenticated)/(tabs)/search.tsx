import { api } from '@/convex/_generated/api';
import { useDebounce } from '@/hooks/useDebounce';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';
import { Feather } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import React, { useMemo, useState } from 'react';
import {
  FlatList,
  Image,
  ListRenderItem,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface ContactItem {
  _id: string;
  name?: string;
  jobTitle?: string;
  location?: string;
  email?: string;
  phone?: string;
  avatarUrl?: string;
  lastContact?: number;
  createdAt: number;
}

type SortOption = 'name' | 'company' | 'tags' | 'lastContacted';

export default function Search() {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [activeSortOption, setActiveSortOption] = useState<SortOption>('name');

  const contacts = useQuery(api.contactSearch.searchContacts, { query: debouncedSearchQuery });

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#000000',
    cardBackground: isDarkMode ? '#2A2A2A' : '#1A1A1A',
    text: isDarkMode ? '#FFFFFF' : '#FFFFFF',
    secondaryText: isDarkMode ? '#A0A0A0' : '#888888',
    searchBackground: isDarkMode ? '#2A2A2A' : '#1A1A1A',
    searchBorder: isDarkMode ? '#3A3A3A' : '#333333',
    buttonBackground: isDarkMode ? '#3A3A3A' : '#2A2A2A',
    buttonText: isDarkMode ? '#FFFFFF' : '#FFFFFF',
    placeholderText: isDarkMode ? '#666666' : '#666666',
  };

  const sortedContacts = useMemo(() => {
    if (!contacts) return [];

    const sorted = [...contacts];

    // Sort based on active sort option
    sorted.sort((a: ContactItem, b: ContactItem) => {
      switch (activeSortOption) {
        case 'name':
          return (a.name || '').localeCompare(b.name || '');
        case 'company':
          return (a.location || '').localeCompare(b.location || '');
        case 'lastContacted':
          return (b.lastContact || 0) - (a.lastContact || 0);
        case 'tags':
          // For now, sort by job title as a proxy for tags
          return (a.jobTitle || '').localeCompare(b.jobTitle || '');
        default:
          return 0;
      }
    });

    return sorted;
  }, [contacts, activeSortOption]);

  const renderSortButton = (option: SortOption, label: string) => {
    const isActive = activeSortOption === option;
    return (
      <TouchableOpacity
        style={[
          styles.sortButton,
          {
            backgroundColor: colors.buttonBackground,
            borderColor: colors.searchBorder,
          },
        ]}
        onPress={() => setActiveSortOption(option)}
      >
        <Text
          style={[
            styles.sortButtonText,
            { color: colors.buttonText },
          ]}
        >
          {label}
        </Text>
        <Feather
          name="chevron-down"
          size={16}
          color={colors.buttonText}
          style={{ marginLeft: 4 }}
        />
      </TouchableOpacity>
    );
  };

  const renderContactItem: ListRenderItem<ContactItem> = ({ item }) => {
    return (
      <TouchableOpacity style={[styles.contactItem, { backgroundColor: colors.background }]}>
        <View style={[styles.avatar, { backgroundColor: colors.buttonBackground }]}>
          {item.avatarUrl ? (
            <Image source={{ uri: item.avatarUrl }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatarPlaceholder, { backgroundColor: colors.buttonBackground }]}>
              <Text style={[styles.avatarText, { color: colors.text }]}>
                {(item.name || 'U').charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>
        <View style={styles.contactInfo}>
          <Text style={[styles.contactName, { color: colors.text }]}>
            {item.name || 'Unknown'}
          </Text>
          <Text style={[styles.contactDetails, { color: colors.secondaryText }]}>
            {item.jobTitle && item.location
              ? `${item.jobTitle} at ${item.location}`
              : item.jobTitle || item.location || item.email || 'No details'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: colors.searchBackground, borderColor: colors.searchBorder, marginTop: 10 }]}>
          <Feather name="search" size={20} color={colors.placeholderText} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search contacts"
            placeholderTextColor={colors.placeholderText}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Sort Buttons */}
      <View style={styles.sortContainer}>
        {renderSortButton('name', 'Name')}
        {renderSortButton('company', 'Company')}
        {renderSortButton('tags', 'Tags')}
        {renderSortButton('lastContacted', 'Last Contacted')}
      </View>

      {/* Contacts List */}
      <FlatList
        data={sortedContacts}
        renderItem={renderContactItem}
        keyExtractor={(item) => item._id}
        style={styles.contactsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  sortContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  sortButtonText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
  },
  contactsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 0,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Poppins_500Medium',
    marginBottom: 4,
  },
  contactDetails: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
  },
});
