import { useContactsEdit } from '@/contexts/ContactsEditContext';
import { api } from '@/convex/_generated/api';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';
import { Feather } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import { Animated, FlatList, Image, ListRenderItem, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';

interface ContactItem {
  id: string;
  name: string;
  company: string;
  lastContact: string;
  avatarUrl?: string;
  email?: string;
  phone?: string;
  jobTitle?: string;
  location?: string;
}

export default function Contacts() {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const { isEditMode, selectedContacts, checkboxAnimation, toggleContactSelection } = useContactsEdit();
  const router = useRouter();

  const contacts = useQuery(api.contacts.getContacts);

  if (contacts === null) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#fff' }}>No contacts found</Text>
      </View>
    );
  }

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#FCFCFC',
    cardBackground: isDarkMode ? '#2A2A2A' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    secondaryText: isDarkMode ? '#A0A0A0' : '#666666',
    border: isDarkMode ? '#3A3A3A' : '#E0E0E0',
    searchBackground: isDarkMode ? '#3A3A3A' : '#F0F0F0',
    activeTab: isDarkMode ? '#4A90E2' : '#2563EB',
    inactiveTab: isDarkMode ? '#666666' : '#999999',
    destructive: '#FF3B30',
    checkboxSelected: '#007AFF',
  };

  const handleContactPress = (contact: ContactItem) => {
    if (isEditMode) {
      toggleContactSelection(contact.id);
    } else {
      router.push(`/contacts/${contact.id}`);
    }
  };

  const renderContactItem: ListRenderItem<ContactItem> = ({ item }) => {
    const isSelected = selectedContacts.has(item.id);
    
    return (
      <TouchableOpacity 
        style={[styles.contactItem, { borderBottomColor: colors.border }]}
        onPress={() => handleContactPress(item)}
      >
        {/* Animated Checkbox */}
        <Animated.View 
          style={[
            styles.checkboxContainer,
            {
              width: checkboxAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 30],
              }),
              opacity: checkboxAnimation,
              marginRight: checkboxAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 12],
              }),
            }
          ]}
        >
          <TouchableOpacity
            style={[
              styles.checkbox,
              {
                backgroundColor: isSelected ? colors.checkboxSelected : 'transparent',
                borderColor: isSelected ? colors.checkboxSelected : colors.border,
              }
            ]}
            onPress={() => toggleContactSelection(item.id)}
          >
            {isSelected && (
              <Feather name="check" size={16} color="white" />
            )}
          </TouchableOpacity>
        </Animated.View>
        
        <Image
          source={{ uri: item.avatarUrl }}
          style={styles.avatar}
        />
        <View style={styles.contactInfo}>
          <Text style={[styles.contactName, { color: colors.text }]}>{item.name}</Text>
          <Text style={[styles.companyName, { color: colors.secondaryText }]}>{item.company}</Text>
          <Text style={[styles.lastContact, { color: colors.secondaryText }]}>Last contact: {item.lastContact}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Contacts List */}
      <FlatList
        data={contacts?.map(contact => ({
          id: contact._id,
          name: contact.email || 'Unknown Contact',
          company: contact.jobTitle || 'No Company',
          lastContact: new Date(contact.updatedAt).toLocaleDateString(),
          avatarUrl: contact.avatarUrl,
          email: contact.email,
          phone: contact.phone,
          jobTitle: contact.jobTitle,
          location: contact.location,
        }))}
        renderItem={renderContactItem}
        keyExtractor={(item) => item.id}
        style={styles.contactList}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  checkboxContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 10,
    paddingHorizontal: 16,
    height: 50,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
  },
  contactList: {
    flex: 1,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 4,
  },
  companyName: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
  },
  lastContact: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
  },
});
