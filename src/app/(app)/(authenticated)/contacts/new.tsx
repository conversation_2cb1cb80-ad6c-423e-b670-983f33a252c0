import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { useMutation } from "convex/react";
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
    Alert,
    Image,
    Pressable,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    View,
    useColorScheme,
} from "react-native";


interface ContactHistory {
  id: string;
  type: 'call' | 'email';
  date: string;
  description: string;
}

export default function NewContact() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  
  const createContact = useMutation(api.contacts.createContact);

  const generatedUploadUrl = useMutation(api.contacts.generatedUploadUrl);

  const avatarUrlFromId = useMutation(api.contacts.avatarUrlFromId);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    jobTitle: '',
    company: '',
    location: '',
    linkedin: '',
    notes: '',
  });

  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  
  const [avatar, setAvatar] = useState<string | null>(null);
  const [contactHistory, setContactHistory] = useState<ContactHistory[]>([]);
  
  const styles = createStyles(isDark);
  
  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
        // ✅ new, non-deprecated way
        mediaTypes: ['images'],   // or IMAGE | VIDEO | AUDIO
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });
    
    if (!result.canceled) {
      setAvatar(result.assets[0].uri);
    }
  };
  
  const addContactHistory = () => {
    const newEntry: ContactHistory = {
      id: Date.now().toString(),
      type: 'call',
      date: new Date().toISOString().split('T')[0],
      description: 'New contact entry'
    };
    setContactHistory([...contactHistory, newEntry]);
  };

  const handleAvatarUpload = async () => {
    if (avatar) {
      const uploadUrl = await generatedUploadUrl();
      const blob = await fetch(avatar).then((res) => res.blob());
      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: { 'Content-Type': blob!.type },
        body: blob,
      });
      if (!response.ok) {
        throw new Error('Failed to upload avatar');
      }
      const { storageId } = await response.json();
      return storageId;
    }
    return null;
  };
  
  const handleCreateContact = async () => {
    try {
      if (!formData.name.trim()) {
        Alert.alert('Error', 'Name is required');
        return;
      }

      let avatarUrl = null;
      const storageId = await handleAvatarUpload();
      
      // Only try to get the URL if we have a valid storageId and an avatar was selected
      if (storageId && avatar != null) {
        try {
          avatarUrl = await avatarUrlFromId({ id: storageId });
        } catch (error) {
          console.error('Error getting avatar URL:', error);
          // Continue without the avatar URL if there's an error
          avatarUrl = null;
        }
      }

      
      // Create a new object with only defined values and proper types
      const contactData = {
        name: formData.name,
        phone: formData.phone,
        ...(formData.email && { email: formData.email }),
        ...(formData.jobTitle && { jobTitle: formData.jobTitle }),
        ...(formData.company && { company: formData.company }),
        ...(formData.location && { location: formData.location }),
        ...(formData.linkedin && { linkedin: formData.linkedin }),
        ...(formData.notes && { notes: formData.notes }),
        ...(selectedTags.length > 0 && { tags: selectedTags as Id<"tags">[] }),
        ...(storageId && { avatar: storageId as Id<"_storage"> }),
        ...(avatarUrl && { avatarUrl })
      };
      
      await createContact(contactData);
      
      Alert.alert('Success', 'Contact created successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to create contact');
      console.error(error);
    }
  };
  
  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Pressable onPress={() => router.back()} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </Pressable>
          <Text style={styles.headerTitle}>Create Profile</Text>
        </View>
        
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <Pressable onPress={pickImage} style={styles.avatarContainer}>
            {avatar ? (
              <Image source={{ uri: avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.defaultAvatar}>
                <Image 
                  source={{ }} 
                  style={styles.avatar} 
                />
              </View>
            )}
            <View style={styles.editBadge}>
              <Text style={[styles.editBadgeText, { fontSize: 20, marginBottom: 5 }]}>-</Text>
            </View>
          </Pressable>
        </View>
        
        {/* Form Fields */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your full name"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.name}
              onChangeText={(value) => updateFormData('name', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email address"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Phone</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your phone number"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              keyboardType="phone-pad"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Job Title</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., UX Analyst"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.jobTitle}
              onChangeText={(value) => updateFormData('jobTitle', value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Company</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., TechCorp Inc."
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.company}
              onChangeText={(value) => updateFormData('company', value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., San Francisco, CA"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.location}
              onChangeText={(value) => updateFormData('location', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>LinkedIn</Text>
            <TextInput
              style={styles.input}
              placeholder="Your LinkedIn profile URL"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.linkedin}
              onChangeText={(value) => updateFormData('linkedin', value)}
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.notesInput]}
              placeholder="Add any relevant notes here..."
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <TagInput
              selectedTags={selectedTags}
              onTagsChange={setSelectedTags}
              placeholder="Add tags..."
            />
          </View>
        </View>
        
        {/* Contact History Section */}
        <View style={styles.historySection}>
          <Text style={styles.sectionTitle}>Contact History</Text>
          
          {contactHistory.map((entry) => (
            <View key={entry.id} style={styles.historyItem}>
              <Text style={styles.historyType}>
                {entry.type === 'call' ? 'Called on' : 'Emailed on'} {entry.date}
              </Text>
              <Text style={styles.historyDescription}>{entry.description}</Text>
            </View>
          ))}
          
          <Pressable onPress={addContactHistory} style={styles.addHistoryButton}>
            <Text style={styles.addHistoryText}>+ Add Contact History</Text>
          </Pressable>
        </View>
        
        {/* Create Button */}
        <Pressable onPress={handleCreateContact} style={styles.createButton}>
          <Text style={styles.createButtonText}>Create Profile</Text>
        </Pressable>
        
        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? '#000' : '#fff',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    left: 0,
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: isDark ? '#fff' : '#000',
    fontWeight: '300',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: isDark ? '#fff' : '#000',
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  defaultAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: isDark ? '#333' : '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: isDark ? '#000' : '#fff',
  },
  editBadgeText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  formSection: {
    marginBottom: 30,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: isDark ? '#fff' : '#000',
    marginBottom: 8,
  },
  input: {
    backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: isDark ? '#fff' : '#000',
    borderWidth: 1,
    borderColor: isDark ? '#333' : '#e0e0e0',
  },
  notesInput: {
    height: 100,
    paddingTop: 14,
  },
  historySection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: isDark ? '#fff' : '#000',
    marginBottom: 16,
  },
  historyItem: {
    backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: isDark ? '#333' : '#e0e0e0',
  },
  historyType: {
    fontSize: 14,
    fontWeight: '500',
    color: isDark ? '#ccc' : '#666',
    marginBottom: 4,
  },
  historyDescription: {
    fontSize: 16,
    color: isDark ? '#fff' : '#000',
  },
  addHistoryButton: {
    borderWidth: 2,
    borderColor: isDark ? '#333' : '#e0e0e0',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addHistoryText: {
    fontSize: 16,
    color: isDark ? '#666' : '#999',
    fontWeight: '500',
  },
  createButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
});