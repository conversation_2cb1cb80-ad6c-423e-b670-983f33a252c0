import React, { useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  Modal,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useTheme } from '@/hooks/useTheme';
import { TagChip } from '@/components/TagChip';
import { useRouter } from 'expo-router';

const TAG_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
  '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
];

export default function ManageTags() {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const router = useRouter();
  
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [selectedColor, setSelectedColor] = useState(TAG_COLORS[0]);
  const [editingTag, setEditingTag] = useState<any>(null);

  const tags = useQuery(api.tags.getTags);
  const createTag = useMutation(api.tags.createTag);
  const updateTag = useMutation(api.tags.updateTag);
  const deleteTag = useMutation(api.tags.deleteTag);

  const colors = {
    background: isDarkMode ? '#000000' : '#FFFFFF',
    secondaryBackground: isDarkMode ? '#1C1C1E' : '#F2F2F7',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    secondaryText: isDarkMode ? '#8E8E93' : '#6D6D70',
    border: isDarkMode ? '#38383A' : '#C6C6C8',
    buttonBackground: isDarkMode ? '#007AFF' : '#007AFF',
  };

  const handleCreateTag = async () => {
    if (!newTagName.trim()) return;

    try {
      await createTag({ name: newTagName.trim(), color: selectedColor });
      setNewTagName('');
      setSelectedColor(TAG_COLORS[0]);
      setIsCreateModalVisible(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to create tag. It may already exist.');
    }
  };

  const handleEditTag = async () => {
    if (!editingTag || !newTagName.trim()) return;

    try {
      await updateTag({
        id: editingTag._id,
        name: newTagName.trim(),
        color: selectedColor,
      });
      setNewTagName('');
      setSelectedColor(TAG_COLORS[0]);
      setEditingTag(null);
      setIsEditModalVisible(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to update tag. Name may already exist.');
    }
  };

  const handleDeleteTag = (tag: any) => {
    Alert.alert(
      'Delete Tag',
      `Are you sure you want to delete "${tag.name}"? This will remove it from all contacts.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTag({ id: tag._id });
            } catch (error) {
              Alert.alert('Error', 'Failed to delete tag.');
            }
          },
        },
      ]
    );
  };

  const openEditModal = (tag: any) => {
    setEditingTag(tag);
    setNewTagName(tag.name);
    setSelectedColor(tag.color);
    setIsEditModalVisible(true);
  };

  const renderColorPicker = () => (
    <View style={styles.colorPicker}>
      <Text style={[styles.modalLabel, { color: colors.text }]}>Color</Text>
      <View style={styles.colorGrid}>
        {TAG_COLORS.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              selectedColor === color && styles.selectedColor,
            ]}
            onPress={() => setSelectedColor(color)}
          >
            {selectedColor === color && (
              <Feather name="check" size={16} color="white" />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderTagItem = ({ item }: { item: any }) => (
    <View style={[styles.tagItem, { backgroundColor: colors.secondaryBackground }]}>
      <TagChip tag={item} size="medium" />
      <View style={styles.tagActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.buttonBackground }]}
          onPress={() => openEditModal(item)}
        >
          <Feather name="edit-2" size={16} color="white" />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF3B30' }]}
          onPress={() => handleDeleteTag(item)}
        >
          <Feather name="trash-2" size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const CreateEditModal = ({ visible, onClose, isEdit = false }: any) => (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.cancelButton, { color: colors.buttonBackground }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: colors.text }]}>
            {isEdit ? 'Edit Tag' : 'Create Tag'}
          </Text>
          <TouchableOpacity onPress={isEdit ? handleEditTag : handleCreateTag}>
            <Text style={[styles.saveButton, { color: colors.buttonBackground }]}>
              {isEdit ? 'Save' : 'Create'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.modalContent}>
          <View style={styles.inputGroup}>
            <Text style={[styles.modalLabel, { color: colors.text }]}>Name</Text>
            <TextInput
              style={[styles.modalInput, { backgroundColor: colors.secondaryBackground, color: colors.text }]}
              placeholder="Enter tag name"
              placeholderTextColor={colors.secondaryText}
              value={newTagName}
              onChangeText={setNewTagName}
            />
          </View>

          {renderColorPicker()}

          <View style={styles.previewContainer}>
            <Text style={[styles.modalLabel, { color: colors.text }]}>Preview</Text>
            {newTagName.trim() && (
              <TagChip
                tag={{ _id: 'preview', name: newTagName.trim(), color: selectedColor }}
                size="medium"
              />
            )}
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Manage Tags</Text>
        <TouchableOpacity onPress={() => setIsCreateModalVisible(true)}>
          <Feather name="plus" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <FlatList
        data={tags}
        renderItem={renderTagItem}
        keyExtractor={(item) => item._id}
        style={styles.tagsList}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
      />

      <CreateEditModal
        visible={isCreateModalVisible}
        onClose={() => {
          setIsCreateModalVisible(false);
          setNewTagName('');
          setSelectedColor(TAG_COLORS[0]);
        }}
      />

      <CreateEditModal
        visible={isEditModalVisible}
        onClose={() => {
          setIsEditModalVisible(false);
          setNewTagName('');
          setSelectedColor(TAG_COLORS[0]);
          setEditingTag(null);
        }}
        isEdit={true}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
  },
  tagsList: {
    flex: 1,
  },
  tagItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  tagActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
  },
  cancelButton: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  saveButton: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
  modalContent: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 24,
  },
  modalLabel: {
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    marginBottom: 8,
  },
  modalInput: {
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  colorPicker: {
    marginBottom: 24,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColor: {
    borderWidth: 3,
    borderColor: 'white',
  },
  previewContainer: {
    alignItems: 'flex-start',
  },
});
