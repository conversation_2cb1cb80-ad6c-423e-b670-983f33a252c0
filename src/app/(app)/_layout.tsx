import { ThemeProvider } from '@/hooks/useTheme';
import { useAuth } from "@clerk/clerk-expo";
import { Redirect, Slot, useSegments } from "expo-router";

const Layout = () => {
    const segments = useSegments();
    const inAuthGroup = segments[1] === '(authenticated)';
    const {isSignedIn} = useAuth();

    if (!isSignedIn && inAuthGroup) {
        return <Redirect href='/login' />
    }

    if (isSignedIn && !inAuthGroup) {
        return <Redirect href='/contacts' />
    }
    
    return (
        <ThemeProvider>
            <Slot />
        </ThemeProvider>
    )
}

export default Layout;
