import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from "expo-router";
import { cssInterop } from 'nativewind';
import { Text, TouchableOpacity } from "react-native";

cssInterop(LinearGradient, {
    className: {
        target: 'style',
    }
})


const CreateButton = (props: any) => {
    const router = useRouter();
    
    const handleCreate = () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        router.push('/(app)/(authenticated)/(modal)/create');
    };


    return (
        <TouchableOpacity onPress={handleCreate} className="flex-1 rounded-xl items-center justify-center">
            <LinearGradient colors={['#FF6B6B', '#4ECDC4']} start={{ x: 0, y:0}} end={{ x: 1, y: 1 }} className="rounded-xl items-center justify-center">
                <Text className="text-white text-lg font-Poppins_600SemiBold p-2">Create</Text>
            </LinearGradient>
        </TouchableOpacity>
    );

};
export default CreateButton;