import { api } from '@/convex/_generated/api';
import { useTheme } from '@/hooks/useTheme';
import { Feather } from '@expo/vector-icons';
import { useMutation, useQuery } from 'convex/react';
import React, { useState } from 'react';
import {
    Al<PERSON>,
    FlatList,
    Modal,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { TagChip } from './TagChip';

interface TagInputProps {
  selectedTags: string[];
  onTagsChange: (tagIds: string[]) => void;
  placeholder?: string;
}

export const TagInput: React.FC<TagInputProps> = ({
  selectedTags,
  onTagsChange,
  placeholder = "Add tags...",
}) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreatingTag, setIsCreatingTag] = useState(false);

  const allTags = useQuery(api.tags.getTags);
  const selectedTagsData = useQuery(api.tags.getTagsByIds, { tagIds: selectedTags });
  const createTag = useMutation(api.tags.createTag);

  const colors = {
    background: isDarkMode ? '#1C1C1E' : '#FFFFFF',
    secondaryBackground: isDarkMode ? '#2C2C2E' : '#F2F2F7',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    secondaryText: isDarkMode ? '#8E8E93' : '#6D6D70',
    border: isDarkMode ? '#38383A' : '#C6C6C8',
    buttonBackground: isDarkMode ? '#007AFF' : '#007AFF',
  };

  const filteredTags = allTags?.filter(tag => 
    tag.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
    !selectedTags.includes(tag._id)
  ) || [];

  const handleTagSelect = (tagId: string) => {
    onTagsChange([...selectedTags, tagId]);
  };

  const handleTagRemove = (tagId: string) => {
    onTagsChange(selectedTags.filter(id => id !== tagId));
  };

  const handleCreateTag = async () => {
    if (!searchQuery.trim()) return;

    setIsCreatingTag(true);
    try {
      const newTag = await createTag({ name: searchQuery.trim() });
      onTagsChange([...selectedTags, newTag]);
      setSearchQuery('');
    } catch {
      Alert.alert('Error', 'Failed to create tag. It may already exist.');
    } finally {
      setIsCreatingTag(false);
    }
  };

  const renderTagItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[styles.tagItem, { backgroundColor: colors.secondaryBackground }]}
      onPress={() => handleTagSelect(item._id)}
    >
      <View style={[styles.colorIndicator, { backgroundColor: item.color }]} />
      <Text style={[styles.tagItemText, { color: colors.text }]}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Selected Tags */}
      {selectedTagsData && selectedTagsData.length > 0 && (
        <View style={styles.selectedTagsContainer}>
          {selectedTagsData.map((tag) => (
            <TagChip
              key={tag._id}
              tag={tag}
              removable
              onRemove={() => handleTagRemove(tag._id)}
              size="medium"
            />
          ))}
        </View>
      )}

      {/* Add Tag Button */}
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.secondaryBackground, borderColor: colors.border }]}
        onPress={() => setIsModalVisible(true)}
      >
        <Feather name="plus" size={16} color={colors.secondaryText} />
        <Text style={[styles.addButtonText, { color: colors.secondaryText }]}>
          {placeholder}
        </Text>
      </TouchableOpacity>

      {/* Tag Selection Modal */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setIsModalVisible(false)}>
              <Text style={[styles.cancelButton, { color: colors.buttonBackground }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Select Tags</Text>
            <View style={{ width: 60 }} />
          </View>

          {/* Search Input */}
          <View style={[styles.searchContainer, { backgroundColor: colors.secondaryBackground }]}>
            <Feather name="search" size={20} color={colors.secondaryText} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder="Search or create tags..."
              placeholderTextColor={colors.secondaryText}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>

          {/* Create New Tag Option */}
          {searchQuery.trim() && !filteredTags.some(tag => tag.name.toLowerCase() === searchQuery.toLowerCase()) && (
            <TouchableOpacity
              style={[styles.createTagButton, { backgroundColor: colors.buttonBackground }]}
              onPress={handleCreateTag}
              disabled={isCreatingTag}
            >
              <Feather name="plus" size={20} color="white" />
              <Text style={styles.createTagText}>
                Create &quot;{searchQuery.trim()}&quot;
              </Text>
            </TouchableOpacity>
          )}

          {/* Tags List */}
          <FlatList
            data={filteredTags}
            renderItem={renderTagItem}
            keyExtractor={(item) => item._id}
            style={styles.tagsList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  selectedTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  addButtonText: {
    marginLeft: 8,
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
  },
  modalContainer: {
    flex: 1,
    paddingTop: 50,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  cancelButton: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  createTagButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createTagText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  tagsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  tagItemText: {
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
});
