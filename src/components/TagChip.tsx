import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Feather } from '@expo/vector-icons';

interface TagChipProps {
  tag: {
    _id: string;
    name: string;
    color: string;
  };
  onRemove?: () => void;
  size?: 'small' | 'medium' | 'large';
  removable?: boolean;
}

export const TagChip: React.FC<TagChipProps> = ({ 
  tag, 
  onRemove, 
  size = 'medium', 
  removable = false 
}) => {
  const getTextColor = (backgroundColor: string) => {
    // Convert hex to RGB
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return black for light colors, white for dark colors
    return luminance > 0.5 ? '#000000' : '#FFFFFF';
  };

  const sizeStyles = {
    small: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      fontSize: 12,
      iconSize: 12,
    },
    medium: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      fontSize: 14,
      iconSize: 14,
    },
    large: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      fontSize: 16,
      iconSize: 16,
    },
  };

  const currentSize = sizeStyles[size];
  const textColor = getTextColor(tag.color);

  return (
    <View 
      style={[
        styles.chip, 
        { 
          backgroundColor: tag.color,
          paddingHorizontal: currentSize.paddingHorizontal,
          paddingVertical: currentSize.paddingVertical,
        }
      ]}
    >
      <Text 
        style={[
          styles.chipText, 
          { 
            color: textColor,
            fontSize: currentSize.fontSize,
          }
        ]}
      >
        {tag.name}
      </Text>
      {removable && onRemove && (
        <TouchableOpacity 
          onPress={onRemove}
          style={styles.removeButton}
          hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
        >
          <Feather 
            name="x" 
            size={currentSize.iconSize} 
            color={textColor} 
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  chipText: {
    fontFamily: 'Poppins_500Medium',
  },
  removeButton: {
    marginLeft: 6,
  },
});
