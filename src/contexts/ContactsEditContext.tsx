import { api } from '@/convex/_generated/api';
import { useMutation } from 'convex/react';
import React, { createContext, ReactNode, useContext, useState } from 'react';
import { Animated } from 'react-native';


interface ContactsEditContextType {
  isEditMode: boolean;
  selectedContacts: Set<string>;
  checkboxAnimation: Animated.Value;
  toggleEditMode: () => void;
  toggleContactSelection: (contactId: string) => void;
  handleDeleteContacts: () => void;
  setSelectedContacts: (contacts: Set<string>) => void;
}

const ContactsEditContext = createContext<ContactsEditContextType | undefined>(undefined);

export const useContactsEdit = () => {
  const context = useContext(ContactsEditContext);
  if (!context) {
    throw new Error('useContactsEdit must be used within a ContactsEditProvider');
  }
  return context;
};

interface ContactsEditProviderProps {
  children: ReactNode;
}

export const ContactsEditProvider: React.FC<ContactsEditProviderProps> = ({ children }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<Set<string>>(new Set());
  const [checkboxAnimation] = useState(new Animated.Value(0));
  const deleteContactMutation = useMutation(api.contacts.deleteContact);

  const toggleEditMode = () => {
    const newEditMode = !isEditMode;
    setIsEditMode(newEditMode);
    setSelectedContacts(new Set());
    
    Animated.timing(checkboxAnimation, {
      toValue: newEditMode ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const toggleContactSelection = (contactId: string) => {
    const newSelected = new Set(selectedContacts);
    if (newSelected.has(contactId)) {
      newSelected.delete(contactId);
    } else {
      newSelected.add(contactId);
    }
    setSelectedContacts(newSelected);
  };

  const handleDeleteContacts = () => {
    // TODO: Implement contact deletion logic
    console.log('Deleting contacts:', Array.from(selectedContacts));
    
    
    deleteContactMutation({ contactIds: Array.from(selectedContacts) });
    
    // Reset to normal state after deletion
    setSelectedContacts(new Set());
    setIsEditMode(false);
    
    Animated.timing(checkboxAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const value: ContactsEditContextType = {
    isEditMode,
    selectedContacts,
    checkboxAnimation,
    toggleEditMode,
    toggleContactSelection,
    handleDeleteContacts,
    setSelectedContacts,
  };

  return (
    <ContactsEditContext.Provider value={value}>
      {children}
    </ContactsEditContext.Provider>
  );
};
